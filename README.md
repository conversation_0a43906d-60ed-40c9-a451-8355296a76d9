# CloudStore - E-commerce Platform

A Spring Boot-based e-commerce platform with OAuth2 Google login, phone number OTP authentication, and role-based access control.

## Features

### Authentication
- **OAuth2 Google Login**: Seamless login with Google accounts
- **Phone Number OTP**: SMS-based authentication using <PERSON><PERSON><PERSON>
- **JWT Token Management**: Secure token-based authentication

### Role-Based Access Control
- **SUPERADMIN**: Full system access, can create stores and manage everything
- **ADMIN**: Can manage their assigned store (categories, products, tax configuration)
- **USER**: Can access all pages except order module
- **GUEST**: Unauthenticated users with limited access

### Core Features
- **Store Management**: SUPERADMIN can create stores with admin assignments
- **Product Catalog**: Categories and products with inventory management
- **Order System**: Complete order management (restricted from USER role)
- **Configurable Tax**: Store-specific tax rate configuration

## Technology Stack

- **Backend**: Spring Boot 3.5.4, Java 21
- **Database**: MySQL
- **Security**: Spring Security with JWT
- **Authentication**: OAuth2 (Google), SMS OTP (Twilio)
- **Build Tool**: Maven

## Setup Instructions

### Prerequisites
- Java 21
- MySQL 8.0+
- Maven 3.6+

### Database Setup
1. Create MySQL database:
```sql
CREATE DATABASE cloudsore;
```

2. Update database credentials in `application.yml`:
```yaml
spring:
  datasource:
    username: your_username
    password: your_password
```

### Google OAuth2 Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth2 credentials
5. Add authorized redirect URI: `http://localhost:8080/login/oauth2/code/google`
6. Update `application.yml`:
```yaml
spring:
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: your-google-client-id
            client-secret: your-google-client-secret
```

### Twilio Setup (for OTP)
1. Create account at [Twilio](https://www.twilio.com/)
2. Get Account SID, Auth Token, and Phone Number
3. Update `application.yml`:
```yaml
twilio:
  account:
    sid: your-twilio-account-sid
  auth:
    token: your-twilio-auth-token
  phone:
    number: your-twilio-phone-number
```

### Running the Application
1. Clone the repository
2. Navigate to project directory
3. Run the application:
```bash
mvn spring-boot:run
```

The application will start on `http://localhost:8080`

## API Documentation

### Swagger UI
Once the application is running, you can access the interactive API documentation at:
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/api-docs

### API Groups
The APIs are organized into the following groups:
- **Public APIs**: Endpoints accessible without authentication
- **Authentication APIs**: Login, registration, and token management
- **Admin APIs**: Store and product management (ADMIN/SUPERADMIN only)
- **User APIs**: User profile and management

### Authentication in Swagger
1. Use the phone registration/login endpoints to get a JWT token
2. Click the "Authorize" button in Swagger UI
3. Enter `Bearer <your-jwt-token>` in the authorization field
4. Now you can test protected endpoints

### Authentication Endpoints

#### Register with Phone Number
```http
POST /api/auth/register/phone
Content-Type: application/json

{
  "name": "John Doe",
  "phoneNumber": "+919876543210"
}
```

#### Login with Phone Number
```http
POST /api/auth/login/phone
Content-Type: application/json

{
  "phoneNumber": "+919876543210"
}
```

#### Verify OTP
```http
POST /api/auth/verify/otp
Content-Type: application/json

{
  "phoneNumber": "+919876543210",
  "otp": "123456"
}
```

#### Get Current User
```http
GET /api/auth/me
Authorization: Bearer <jwt-token>
```

### Store Management (SUPERADMIN only)

#### Create Store
```http
POST /api/stores/create
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "My Store",
  "description": "Store description",
  "address": "Store address",
  "adminEmail": "<EMAIL>"
}
```

#### Get All Stores
```http
GET /api/stores
```

#### Update Tax Rate (ADMIN/SUPERADMIN)
```http
PUT /api/stores/{storeId}/tax
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "taxRate": 18.0
}
```

### Product Management (ADMIN/SUPERADMIN)

#### Create Category
```http
POST /api/stores/{storeId}/categories
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "Electronics",
  "description": "Electronic items",
  "imageUrl": "https://example.com/image.jpg"
}
```

#### Create Product
```http
POST /api/stores/{storeId}/products
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "Smartphone",
  "description": "Latest smartphone",
  "price": 599.99,
  "discountPrice": 549.99,
  "stockQuantity": 100,
  "imageUrl": "https://example.com/phone.jpg",
  "categoryId": 1
}
```

#### Get Products
```http
GET /api/stores/{storeId}/products
GET /api/stores/{storeId}/products?categoryId=1
```

### Public Endpoints (No authentication required)

#### Get Public Stores
```http
GET /api/public/stores
```

#### Get Public Products
```http
GET /api/public/stores/{storeId}/products
GET /api/public/products/featured
```

### Order Management (ADMIN/SUPERADMIN only)

#### Get Orders
```http
GET /api/orders
Authorization: Bearer <jwt-token>
```

#### Update Order Status
```http
PUT /api/orders/{orderId}/status
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "status": "CONFIRMED"
}
```

## Role-Based Access Summary

| Endpoint | SUPERADMIN | ADMIN | USER | GUEST |
|----------|------------|-------|------|-------|
| Store Creation | ✅ | ❌ | ❌ | ❌ |
| Store Management | ✅ | ✅ (own store) | ❌ | ❌ |
| Product Management | ✅ | ✅ (own store) | ❌ | ❌ |
| View Products | ✅ | ✅ | ✅ | ✅ |
| Order Management | ✅ | ✅ (own store) | ❌ | ❌ |
| User Management | ✅ | ❌ | ❌ | ❌ |

## Swagger/OpenAPI Features

### Interactive Documentation
- **Swagger UI**: Complete interactive API documentation
- **Try it out**: Test APIs directly from the browser
- **Authentication**: Built-in JWT token authentication
- **API Groups**: Organized by functionality (Public, Auth, Admin, User)
- **Schema Documentation**: Detailed request/response models

### Swagger Endpoints
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI Spec**: http://localhost:8080/api-docs
- **Health Check**: http://localhost:8080/api/public/health

### Using Swagger UI
1. Open http://localhost:8080/swagger-ui.html
2. Register/Login using phone number endpoints
3. Copy the JWT token from the response
4. Click "Authorize" button and enter: `Bearer <your-token>`
5. Test protected endpoints

## Development Notes

- The application uses JWT tokens for authentication
- OTP codes expire in 5 minutes
- For development, OTP codes are logged to console if Twilio is not configured
- Database tables are auto-created using JPA
- CORS is enabled for all origins (configure for production)
- Swagger UI is accessible without authentication for easy testing

## Security Considerations

- Update JWT secret in production
- Configure proper CORS origins
- Use HTTPS in production
- Secure Twilio credentials
- Implement rate limiting for OTP requests
