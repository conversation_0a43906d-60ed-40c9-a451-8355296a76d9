package in.xtrs.cloudsore.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Authentication response object")
public class AuthResponse {

    @Schema(description = "JWT token for authenticated requests", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "Response message", example = "OTP sent successfully")
    private String message;

    @Schema(description = "Indicates if the operation was successful", example = "true")
    private boolean success;
}
