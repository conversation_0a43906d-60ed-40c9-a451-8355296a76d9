package in.xtrs.cloudsore.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request object for phone number registration")
public class AuthRequest {

    @Schema(description = "User's full name", example = "John Doe", required = true)
    @NotBlank(message = "Name is required")
    private String name;

    @Schema(description = "User's phone number with country code", example = "+919876543210", required = true)
    @NotBlank(message = "Phone number is required")
    private String phoneNumber;
}
