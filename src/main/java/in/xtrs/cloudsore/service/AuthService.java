package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.Role;
import in.xtrs.cloudsore.exception.BadRequestException;
import in.xtrs.cloudsore.exception.ResourceNotFoundException;
import in.xtrs.cloudsore.exception.UnauthorizedException;
import in.xtrs.cloudsore.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {

    private final UserService userService;
    private final JwtUtil jwtUtil;

    public String authenticateWithPhone(String phoneNumber, String otp) {
        if (!userService.verifyOtp(phoneNumber, otp)) {
            throw new UnauthorizedException("Invalid OTP");
        }
        
        User user = userService.findByPhoneNumber(phoneNumber);
        return jwtUtil.generateToken(user.getPhoneNumber(), user.getRole().getValue(), user.getId());
    }

    public String registerWithPhone(String name, String phoneNumber) {
        // Check if user already exists
        Optional<User> existingUser = userService.findByPhoneNumberOptional(phoneNumber);
        if (existingUser.isPresent()) {
            throw new RuntimeException("User already exists with this phone number");
        }
        
        // Create new user
        User user = userService.createUserWithPhone(name, phoneNumber, Role.USER);
        
        // Send OTP
        boolean otpSent = userService.sendOtpToUser(phoneNumber);
        if (!otpSent) {
            throw new RuntimeException("Failed to send OTP");
        }
        
        return "OTP sent successfully";
    }

    public String loginWithPhone(String phoneNumber) {
        // Check if user exists
        Optional<User> userOpt = userService.findByPhoneNumberOptional(phoneNumber);
        if (userOpt.isEmpty()) {
            throw new ResourceNotFoundException("User not found. Please register first.");
        }

        // Send OTP
        boolean otpSent = userService.sendOtpToUser(phoneNumber);
        if (!otpSent) {
            throw new BadRequestException("Failed to send OTP");
        }
        
        return "OTP sent successfully";
    }

    public String authenticateWithGoogle(String googleId, String email, String name, String profilePicture) {
        User user = userService.createOrUpdateGoogleUser(googleId, email, name, profilePicture);
        return jwtUtil.generateToken(user.getEmail(), user.getRole().getValue(), user.getId());
    }

    public User getCurrentUser(String token) {
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        String username = jwtUtil.extractUsername(token);
        Long userId = jwtUtil.extractUserId(token);
        
        return userService.findById(userId);
    }

    public boolean hasRole(String token, Role requiredRole) {
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        String userRole = jwtUtil.extractRole(token);
        Role role = Role.valueOf(userRole);
        
        // SUPERADMIN has access to everything
        if (role == Role.SUPERADMIN) {
            return true;
        }
        
        // Check specific role
        return role == requiredRole;
    }

    public boolean hasAnyRole(String token, Role... roles) {
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        String userRole = jwtUtil.extractRole(token);
        Role role = Role.valueOf(userRole);
        
        // SUPERADMIN has access to everything
        if (role == Role.SUPERADMIN) {
            return true;
        }
        
        // Check if user has any of the required roles
        for (Role requiredRole : roles) {
            if (role == requiredRole) {
                return true;
            }
        }
        
        return false;
    }

    public boolean isTokenValid(String token) {
        try {
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }
            
            String username = jwtUtil.extractUsername(token);
            return jwtUtil.validateToken(token, username);
        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return false;
        }
    }
}
