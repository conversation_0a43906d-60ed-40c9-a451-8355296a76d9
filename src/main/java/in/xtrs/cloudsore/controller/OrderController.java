package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.*;
import in.xtrs.cloudsore.entity.Order;
import in.xtrs.cloudsore.entity.OrderItem;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.OrderStatus;
import in.xtrs.cloudsore.enums.Role;
import in.xtrs.cloudsore.exception.ForbiddenException;
import in.xtrs.cloudsore.service.AuthService;
import in.xtrs.cloudsore.service.OrderService;
import in.xtrs.cloudsore.service.StoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@PreAuthorize("hasAnyRole('SUPERADMIN', 'ADMIN')") // USER role cannot access orders as per requirement
public class OrderController {

    private final OrderService orderService;
    private final StoreService storeService;
    private final AuthService authService;

    @PostMapping
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<Order> createOrder(@Valid @RequestBody CreateOrderRequest request,
                                           @RequestHeader("Authorization") String token) {
        try {
            User currentUser = authService.getCurrentUser(token);
            Store store = storeService.findById(request.getStoreId());
            
            Order order = orderService.createOrder(
                currentUser,
                store,
                request.getDeliveryAddress(),
                request.getDeliveryPhone(),
                request.getNotes()
            );
            
            return ResponseEntity.ok(order);
        } catch (Exception e) {
            log.error("Order creation failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping
    public ResponseEntity<List<Order>> getOrders(@RequestHeader("Authorization") String token,
                                                @RequestParam(required = false) Long storeId,
                                                @RequestParam(required = false) OrderStatus status) {
        try {
            User currentUser = authService.getCurrentUser(token);
            List<Order> orders;
            
            if (currentUser.getRole().name().equals("ADMIN")) {
                // Admin can only see orders from their store
                Store store = storeService.findByAdmin(currentUser);
                orders = orderService.getOrdersByStore(store);
            } else {
                // SUPERADMIN can see all orders
                if (storeId != null) {
                    Store store = storeService.findById(storeId);
                    orders = orderService.getOrdersByStore(store);
                } else if (status != null) {
                    orders = orderService.getOrdersByStatus(status);
                } else {
                    // Return empty list for now - could implement getAllOrders if needed
                    orders = List.of();
                }
            }
            
            return ResponseEntity.ok(orders);
        } catch (Exception e) {
            log.error("Get orders failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Order> getOrder(@PathVariable Long id,
                                        @RequestHeader("Authorization") String token) {
        try {
            Order order = orderService.findById(id);
            User currentUser = authService.getCurrentUser(token);
            
            // Check if admin can access this order (must be from their store)
            if (currentUser.getRole().name().equals("ADMIN")) {
                Store adminStore = storeService.findByAdmin(currentUser);
                if (!order.getStore().getId().equals(adminStore.getId())) {
                    return ResponseEntity.forbidden().build();
                }
            }
            
            return ResponseEntity.ok(order);
        } catch (Exception e) {
            log.error("Get order failed: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/{id}/items")
    public ResponseEntity<List<OrderItem>> getOrderItems(@PathVariable Long id,
                                                       @RequestHeader("Authorization") String token) {
        try {
            Order order = orderService.findById(id);
            User currentUser = authService.getCurrentUser(token);
            
            // Check if admin can access this order
            if (currentUser.getRole().name().equals("ADMIN")) {
                Store adminStore = storeService.findByAdmin(currentUser);
                if (!order.getStore().getId().equals(adminStore.getId())) {
                    return ResponseEntity.forbidden().build();
                }
            }
            
            List<OrderItem> orderItems = orderService.getOrderItems(id);
            return ResponseEntity.ok(orderItems);
        } catch (Exception e) {
            log.error("Get order items failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/status")
    public ResponseEntity<Order> updateOrderStatus(@PathVariable Long id,
                                                  @Valid @RequestBody UpdateOrderStatusRequest request,
                                                  @RequestHeader("Authorization") String token) {
        try {
            User currentUser = authService.getCurrentUser(token);
            Order order = orderService.findById(id);
            
            // Check if admin can update this order
            if (currentUser.getRole().name().equals("ADMIN")) {
                Store adminStore = storeService.findByAdmin(currentUser);
                if (!order.getStore().getId().equals(adminStore.getId())) {
                    return ResponseEntity.forbidden().build();
                }
            }
            
            Order updatedOrder = orderService.updateOrderStatus(id, request.getStatus());
            return ResponseEntity.ok(updatedOrder);
        } catch (Exception e) {
            log.error("Update order status failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<Void> cancelOrder(@PathVariable Long id) {
        try {
            orderService.cancelOrder(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Cancel order failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}
