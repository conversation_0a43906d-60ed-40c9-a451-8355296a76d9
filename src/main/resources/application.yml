spring:
  application:
    name: cloudsore
  
  # Database Configuration
  datasource:
    url: *************************************************************************************************************
    username: root
    password:
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
  
  # OAuth2 Google Configuration
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: your-google-client-id
            client-secret: your-google-client-secret
            scope:
              - profile
              - email
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"

# JWT Configuration
jwt:
  secret: x&u^3H7hskRR7888
  expiration: 86400000

# Fast2SMS Configuration (for OTP)
# Use environment variables for production: FAST2SMS_API_KEY, FAST2SMS_SENDER_ID, FAST2SMS_BASE_URL
fast2sms:
  api:
    key: ${FAST2SMS_API_KEY:3DGdYvT7wl6sp4IfmKrxP92hZEMnbRqW5gzUBeAkVuXOL1oJCiJjOWMfDw3y1ZxVbBnaNPQCFEKUo7vS}
  sender:
    id: ${FAST2SMS_SENDER_ID:FSTSMS}
  base:
    url: ${FAST2SMS_BASE_URL:https://www.fast2sms.com/dev/bulkV2}

# Server Configuration
server:
  port: 8080

# Logging Configuration (optional)
logging:
  level:
    in.xtrs.cloudsore: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operationsSorter: method
    tagsSorter: alpha
    tryItOutEnabled: true
    filter: true
  show-actuator: true
  group-configs:
    - group: 'public'
      display-name: 'Public APIs'
      paths-to-match: '/api/public/**'
    - group: 'auth'
      display-name: 'Authentication APIs'
      paths-to-match: '/api/auth/**'
    - group: 'admin'
      display-name: 'Admin APIs'
      paths-to-match:
        - '/api/stores/**'
        - '/api/orders/**'
    - group: 'user'
      display-name: 'User APIs'
      paths-to-match: '/api/users/**'

# Management endpoints (for monitoring)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,openapi,swagger-ui
  endpoint:
    health:
      show-details: when-authorized
